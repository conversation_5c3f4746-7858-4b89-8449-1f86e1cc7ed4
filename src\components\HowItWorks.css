/* Slider specific styles */
.main {
  width: 100%;
  overflow: hidden;
  position: relative;
}

.slider-wrap {
  width: 100%;
  overflow: hidden;
}

.slider-list {
  position: relative;
  height: 450px;
  width: 100%;
}

.slider-slide {
  position: absolute;
  top: 0;
  height: 400px;
  width: 600px;
}

/* Simple round buttons */
.button {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.button:hover {
  opacity: 0.9;
}

/* Arrow icon styles */
.button-arrow {
  width: 1rem;
  height: 1rem;
}