const CTA = () => {
  return (
    <section className="min-h-screen bg-[#070709] text-white flex items-center justify-center px-4">
      <div className="text-center">
        <h2 className="font-['CalSans'] text-[80px] text-white tracking-[0.02em] mb-12 max-w-[1000px]">
          The best AI systems< br />are build side by side.
        </h2>
        
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <button className="flex items-center justify-center gap-2 h-10 px-6 bg-white text-[#0d0e12] text-base font-medium rounded-full hover:bg-opacity-90 transition-all duration-200">
            Get Started
            <img src="/icons/button-arrow-right.svg" alt="" width={20} height={20} className="w-5 h-5" />
          </button>
          
          <button className="flex items-center justify-center gap-2 h-10 px-6 bg-white text-[#0d0e12] text-base font-medium rounded-full hover:bg-opacity-90 transition-all duration-200">
            Contact Us
            <img src="/icons/button-arrow-right.svg" alt="" width={20} height={20} className="w-5 h-5" />
          </button>
        </div>
      </div>
    </section>
  );
};

export default CTA;
