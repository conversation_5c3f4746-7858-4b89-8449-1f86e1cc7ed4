import React from 'react';

const Navigation = () => {
  return (
    <nav className="absolute top-0 left-0 w-full py-4 z-10">
      <div className="w-[1344px] h-12 mx-auto flex items-center">
        {/* Logo */}
        <div className="flex-shrink-0">
          <img src="/icons/BLCKS_Logo.svg" alt="BLCKS" width={80} height={24} />
        </div>
        
        {/* Navigation Links */}
        <div className="hidden md:flex items-center ml-[100px] space-x-6">
          <a href="#" className="flex items-center text-white text-sm font-geist font-normal tracking-[-0.02em] hover:text-gray-300 transition-colors">
            Home
          </a>
          <a href="#" className="flex items-center text-white text-sm font-geist font-normal tracking-[-0.02em] hover:text-gray-300 transition-colors">
            Lösungen
            <img src="/icons/button-arrow-down-white.svg" alt="" className="ml-1 w-4 h-4" />
          </a>
          <a href="#" className="flex items-center text-white text-sm font-geist font-normal tracking-[-0.02em] hover:text-gray-300 transition-colors">
            Consulting
          </a>
          <a href="#" className="flex items-center text-white text-sm font-geist font-normal tracking-[-0.02em] hover:text-gray-300 transition-colors">
            Preise
          </a>
          <a href="#" className="flex items-center text-white text-sm font-geist font-normal tracking-[-0.02em] hover:text-gray-300 transition-colors">
            Ressourcen
            <img src="/icons/button-arrow-down-white.svg" alt="" className="ml-1 w-4 h-4" />
          </a>
          <a href="#" className="flex items-center text-white text-sm font-geist font-normal tracking-[-0.02em] hover:text-gray-300 transition-colors">
            Blcks
            <img src="/icons/button-arrow-down-white.svg" alt="" className="ml-1 w-4 h-4" />
          </a>
        </div>
        
        {/* Right Side Buttons */}
        <div className="ml-auto flex items-center space-x-4">
          <button className="text-white text-sm font-geist font-normal tracking-[-0.02em] hover:text-gray-300 transition-colors">
            Kontakt
          </button>
          <button className="w-[94px] h-7 flex items-center justify-center border border-[#2e3038] rounded-full text-white text-sm font-geist font-normal tracking-[-0.02em] hover:border-gray-400 transition-colors">
            Get started
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
