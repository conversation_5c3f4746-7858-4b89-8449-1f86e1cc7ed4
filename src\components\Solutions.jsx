const FeatureItem = ({ text }) => (
  <div className="flex items-start gap-3 mb-2">
    <img 
      src="/solutions/circle-check.svg" 
      alt="Checkmark" 
      className="w-4 h-4 mt-0.5 flex-shrink-0"
    />
    <span className="text-[11px] text-[#787C87] font-['Geist'] font-normal tracking-[0.01em]">
      {text}
    </span>
  </div>
);

const ProductCard = ({ icon, title, description, features }) => (
  <div className="relative w-[275px] h-[275px] rounded-lg flex-shrink-0 p-4 flex flex-col overflow-hidden">
    {/* Background Image */}
    <div className="absolute inset-0 w-full h-full z-0">
      <img 
        src="/solutions/ProductCardBg.png" 
        alt="" 
        className="w-full h-full object-cover"
      />
    </div>
    {/* Content Container */}
    <div className="relative z-10 flex flex-col h-full">
    {/* Header with Icon and Title */}
    <div className="flex items-start gap-3 mb-4">
      <img 
        src={`/solutions/${icon}.svg`} 
        alt={title} 
        className="w-10 h-10 mt-0.5"
      />
      <div>
        <h4 className="text-white text-base font-normal tracking-[0.01em]">
          {title}
        </h4>
        <p className="text-[11px] text-[#787C87] font-['Geist'] font-normal tracking-[0.01em] mt-1 whitespace-pre-line">
          {description.replace(/<br \/>/g, '\n')}
        </p>
      </div>
    </div>
    
    <div className="mt-4 flex-grow flex flex-col">
      {/* FullLine SVG */}
      <div className="mb-3 w-full">
        <img 
          src="/solutions/FullLineNew.svg" 
          alt="" 
          className="w-full h-auto"
        />
      </div>
      
      {/* Features List */}
      <div className="flex-grow">
        {features.map((feature, index) => (
          <FeatureItem key={index} text={feature} />
        ))}
      </div>
    </div>
    </div>
  </div>
);

const Solutions = () => {
  const solutions = [
    {
      title: "Sales.Block.",
      products: [
        { 
          id: 1,
          icon: "salespipeline",  // SVG exists
          title: "KI Sales Pipeline",
          description: "Automatisierte Leadgenerierung, Outreach und Pipeline-Management",
          features: [
            "Lead Generierung & Qualifikaton",
            "Multi-Channel Outreach",
            "Follow-Ups & Terminbuchungen",
            "KI Kampagnenoptimierung"
          ]
        },
        { 
          id: 2,
          icon: "leadgen",  // SVG exists
          title: "KI Leadgenerierung",
          description: "Finde neue Kundenpotenziale automatisch und zielgerichtet.",
          features: [
            "Automatische Leadrecherche",
            "Lead Enrichment",
            "Lead Scoring & Priorisierung",
            "Automatisierte Berichte"
          ]
        },
        { 
          id: 3,
          icon: "coldemail",  // SVG exists
          title: "KI Cold Email Systems",
          description: "Personalisierte und skalierbare Outreach-Kampagnen.",
          features: [
            "Verfassen personalisierter E-Mails",
            "A/B-Testing für Betreffzeilen und Texte",
            "Automatische Follow-ups",
            "Öffnungs- und Antwortratenoptimierung"
          ]
        },
        { 
          id: 4,
          icon: "contentsystems",  // SVG exists
          title: "KI Content Systems",
          description: "Skaliere Content für Vertrieb und Marketing mit KI.",
          features: [
            "Automatische Content Erstellung",
            "Automatische Blogerstellung",
            "Dynamische Anpassung an Zielgruppen",
            "Content Performance Tracking"
          ]
        }
      ]
    },
    {
      title: "Office.Block.",
      products: [
        { 
          id: 5,
          icon: "email",  // SVG exists
          title: "E-Mail Automatisierung",
          description: "Sag Adieu zum Postfach-Chaos.<br />E-Mails, die sich selbst organisieren.",
          features: [
            "Automatisches Kategorisieren und Priorisieren",
            "Intelligente E-Mail Beantwortung",
            "Intent- und Sentiment Analyse",
            "Anbindung an deine Tools"
          ]
        },
        { 
          id: 6,
          icon: "agenticworkflows",  // SVG exists
          title: "Agentic Workflows",
          description: "Arbeitsprozesse, die sich selbst ausführen – von A bis Z.",
          features: [
            "Automatisierte Auftragseingänge & Fullfillment",
            "Intelligente Zuweisung & Entscheidungen",
            "Status-Updates in Echtzeit",
            "Self-Learning-Workflows"
          ]
        },
        { 
          id: 7,
          icon: "documents",  // SVG exists
          title: "Dokumentenmanagement",
          description: "Dokumente finden, erstellen und verwalten – blitzschnell und smart.",
          features: [
            "Automatische Klassifizierung & Ablage",
            "Volltext-Suche über alle Dateien",
            "Versionskontrolle & Freigabeprozesse",
            "Sicherheit & Compliance integriert"
          ]
        },
        { 
          id: 8,
          icon: "rechnung",  // SVG exists
          title: "Rechnungsmanagement",
          description: "Buchhaltung ohne Kopfschmerzen – Rechnungen laufen von selbst.",
          features: [
            "Automatisches Erkennen von Rechnungen & Belegen",
            "Matching mit Bestellungen & Verträgen",
            "Zahlungserinnerungen und Mahnwesen",
            "Export in Buchhaltungssoftware"
          ]
        }
      ]
    },
    {
      title: "Insight.Block.",
      products: [
        { 
          id: 9,
          icon: "companybrain",  // SVG exists
          title: "KI Company Brain",
          description: "Das zentrale Gedächtnis deines Unternehmens. Wissen in Sekunden.",
          features: [
            "Einheitliche Wissensdatenbank",
            "Q&A-KI Assistent für schnelle Antworten",
            "Kontextuelle Suche & Dokumentenzusammenfassungen",
            "Automatische Reporterstellung"
          ]
        },
        { 
          id: 10,
          icon: "analytics",  // SVG exists
          title: "KI Business Analytics",
          description: "Analysen in Echtzeit – verständlich und handlungsorientiert.",
          features: [
            "KPI-Dashboards automatisch aktualisiert",
            "Drill-down Analysen nach Abteilungen",
            "Prognosen auf Basis historischer Daten",
            "Alerts bei Abweichungen und Trends"
          ]
        },
        { 
          id: 11,
          icon: "predictiveinsights",  // SVG exists
          title: "KI Predictive Insights",
          description: "Sieh Chancen & Risiken, bevor sie entstehen.",
          features: [
            "Umsatzprognosen & Nachfrageplanung",
            "Churn Prediction für Kundenabwanderung",
            "Identifizierung von Wachstumssegmenten",
            "Risikoanalyse & Frühwarnsysteme"
          ]
        },
        { 
          id: 12,
          icon: "automatedseo",  // SVG exists
          title: "KI Automated SEO",
          description: "Optimiere deine Sichtbarkeit in Suchmaschinen automatisch.",
          features: [
            "KI Keyword- und Content-Analyse",
            "KI Blogerstellung & Blogposting",
            "KI-optimierte Meta-Titel & Beschreibungen",
            "Monitoring von Rankings & Wettbewerbern"
          ]
        }
      ]
    },
    {
      title: "Service.Block.",
      products: [
        { 
          id: 13,
          icon: "kundenservice",  // SVG exists
          title: "KI Kundenservice Agent",
          description: "Dein virtueller 24/7-Support – schnell, freundlich und skalierbar.",
          features: [
            "Intelligente FAQ & Service-Antworten",
            "Automatisierte Aktionen (Terminbuchungen etc.)",
            "Multichannel-Support (Website, Chat, Mail, Social)",
            "Lernende Wissensbasis mit Memory-Funktion"
          ]
        },
        { 
          id: 14,
          icon: "support",  // SVG exists
          title: "Supportautomatisierung",
          description: "Weniger Tickets, mehr gelöste Probleme.",
          features: [
            "Automatisches Ticket-Routing",
            "Intelligente Priorisierung nach Dringlichkeit",
            "Self-Service-Lösungen für Kunden",
            "Integration mit Helpdesk-Tools"
          ]
        },
        { 
          id: 15,
          icon: "feedback",  // SVG exists
          title: "KI Feedback & Umfragen",
          description: "Kundenzufriedenheit messen – ohne manuelle Nachfragen.",
          features: [
            "Automatische Versendung von Feedback-Umfragen",
            "Sentiment-Analyse von Antworten",
            "Reports zu NPS & Kundenzufriedenheit",
            "Handlungsempfehlungen aus Daten"
          ]
        },
        { 
          id: 16,
          icon: "intake",  // SVG exists
          title: "KI HR Intake Systems",
          description: "Von Bewerbung bis Onboarding – alles läuft automatisch.",
          features: [
            "Automatisiertes Screening von Bewerbungen",
            "CV-Parsing & Matching mit Stellenprofilen-Analyse",
            "Onboarding-Checklisten für neue Mitarbeiter",
            "Bewerberkommunikation per Chatbot"
          ]
        }
      ]
    },
  ];

  return (
    <section className="min-h-screen bg-[#070709] text-white py-20 px-4">
      <div className="max-w-[1280px] mx-auto">
        {/* Header Section */}
        <div className="mb-12">
          <span className="font-['Geist'] font-light text-sm text-white tracking-[0.02em] pb-3 block">
            MEHR ALS CONSULTNG
          </span>
          <h2 className="font-['Geist'] font-medium text-[48px] text-white tracking-[-0.02em] text-left mb-6">
            We don’t just guide. We build.
          </h2>
          <p className="font-['Geist'] font-normal text-base text-[#ADB0B9] max-w-[700px] pb-[54px]">
            Wir zeigen dir nicht nur, wo KI den größten Hebel für dein Business hat. Wir setzen die Lösungen auch direkt für dich um. Inhouse, maßgeschneidert und nahtlos integriert in deine bestehenden Systeme. So bekommst du nicht nur eine Roadmap, sondern echte Resultate.
          </p>
        </div>

        {/* Solutions Cards */}
        <div className="max-w-[1280px] mx-auto space-y-8">
          {solutions.map((solution, index) => (
            <div 
              key={index}
              className="bg-[#0D0E12] rounded-2xl w-full h-[542px] relative overflow-hidden"
            >
              {/* Product Cards */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full">
                <div className="flex justify-center items-center gap-6 px-4">
                  {solution.products.map((product) => (
                    <ProductCard 
                      key={product.id}
                      icon={product.icon}
                      title={product.title}
                      description={product.description}
                      features={product.features}
                    />
                  ))}
                </div>
              </div>
              
              {/* Title and Button */}
              <div className="absolute bottom-20 left-0 right-0 text-center">
                <h3 className="font-['CalSans'] text-[88px] text-white pb-1.5">
                  {solution.title}
                </h3>
                <button className="mt-8 mx-auto flex items-center justify-center gap-2 h-10 px-6 bg-white text-[#0d0e12] text-base font-medium rounded-full hover:bg-opacity-90 transition-all duration-200">
                  Get started
                  <img src="/icons/button-arrow-right.svg" alt="" width={20} height={20} className="w-5 h-5" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Solutions;
