.marquee {
  width: 100%;
  height: 2.5rem;
  pointer-events: none;
  overflow: hidden;
  margin-top: 0.5rem;
}

.marquee__inner-wrap {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
}

.marquee__inner {
  height: 100%;
  width: fit-content;
  display: flex;
  align-items: center;
  position: relative;
  animation: marquee 50s linear infinite;
  will-change: transform;
  padding: 0 2rem;
}

.marquee__img {
  height: 1.25rem; /* h-5 */
  width: auto;
  margin: 0 2rem;
  object-fit: contain;
  opacity: 0.7;
  transition: all 0.3s ease;
  flex-shrink: 0;
  display: block;
}

.marquee__img:hover {
  filter: grayscale(0) brightness(1);
  opacity: 1;
  transform: scale(1.1);
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-50%);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .marquee {
    height: 2rem;
  }
  
  .marquee__img {
    height: 1.25rem;
    margin: 0 1rem;
  }
}

@media (max-width: 480px) {
  .marquee {
    height: 1.75rem;
  }
  
  .marquee__img {
    height: 1.25rem;
    margin: 0 0.75rem;
  }
}

/* Ensure SVG logos are visible */
svg {
  display: block;
  height: 100%;
  width: auto;
}
