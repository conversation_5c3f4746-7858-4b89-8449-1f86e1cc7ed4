import { useEffect, useRef } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

const Niches = () => {
  const sectionRef = useRef(null);
  const gridRef = useRef(null);

  // Niche items with their corresponding SVG icons in German
  const nicheItems = [
    { text: 'Landwirtschaft', icon: 'tractor.svg' },
    { text: 'Human Resources', icon: 'users.svg' },
    { text: 'Bildung', icon: 'graduation-cap.svg' },
    { text: 'Versicherung', icon: 'house.svg' },
    { text: 'Immobilien', icon: 'building-2.svg' },
    { text: 'Hotellerie', icon: 'hotel.svg' },
    { text: 'Recht', icon: 'gavel.svg' },
    { text: 'Industrie', icon: 'working-factory-svgrepo-com 1.svg' },
    { text: 'Bauwesen', icon: 'pickaxe.svg' },
    { text: 'Gastronomie', icon: 'hamburger.svg' },
    { text: 'Gemeinden', icon: 'landmark.svg' },
    { text: 'Telekommunikation', icon: 'phone-call.svg' },
    { text: 'Einzelhandel', icon: 'store.svg' },
    { text: 'Gesundheitswesen', icon: 'hospital.svg' },
    { text: 'E-Commerce', icon: 'shopping-cart.svg' },
    { text: 'Finanzen', icon: 'piggy-bank.svg' },
    { text: 'Marketing', icon: 'monitor-play.svg' },
    { text: 'Energie', icon: 'house-plug.svg' },




  ].map(item => ({
    ...item,
    icon: (
      <img
        src={`/niches/${item.icon}`}
        alt={item.text}
        width="32"
        height="32"
        className="text-white"
      />
    )
  }));

  // Animation setup
  useEffect(() => {
    const cards = gridRef.current?.children;
    if (!cards) return;

    // Set initial random positions for cards
    gsap.set(cards, {
      x: () => gsap.utils.random(-300, 300),
      y: () => gsap.utils.random(-150, 150),
      rotation: () => gsap.utils.random(-20, 20),
      opacity: 0.6,
      scale: 0.8
    });

    // Create timeline for smooth animation
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: sectionRef.current,
        start: "top 70%",
        end: "center 30%",
        scrub: 1.5,
        markers: false
      }
    });

    // Animate cards into grid positions
    tl.to(cards, {
      x: 0,
      y: 0,
      rotation: 0,
      opacity: 1,
      scale: 1,
      duration: 1,
      ease: "power2.out",
      stagger: {
        amount: 0.8,
        from: "random"
      }
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section ref={sectionRef} className="bg-[#000000] text-white px-4 py-20 mt-[-600px] relative z-30">
      {/* Gradient Overlay */}
      <div className="absolute top-0 h-[570px] w-full bg-[radial-gradient(51.31%_97.15%_at_50%_2.85%,rgba(7,7,9,0.00)_38.06%,#070709_76.52%)] sm:h-[270px]" aria-hidden="true"></div>

      <div className="max-w-7xl mx-auto text-center relative z-10">
        <div className="flex flex-col items-center justify-center">
          <div ref={gridRef} className="grid grid-cols-9 gap-[7.5px] max-w-[1140px] mx-auto">
          {nicheItems.map((item, index) => (
            <div 
              key={index}
              className="bg-[#0D0E12] rounded-lg w-[120px] h-[120px] flex flex-col items-center justify-center gap-2"
            >
              <div className="w-8 h-8">
                {item.icon}
              </div>
              <span className="font-['Geist'] text-[12px] font-light text-[#787c87]">
                {item.text}
              </span>
            </div>
          ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Niches;
