import { useEffect, useRef } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Inject CSS for radial grid spotlight effect
const radialGridStyles = `
  .radial-grid {
    --mx: 50%;
    --my: 50%;
    --diameter: 8;
    --spacing: 14;
    --size: 12;
    --spread: 40;
    --light-color: #fff7a3;
    --bg-color: #0a0a0a;
    --halftone: 0;
    background: var(--bg-color);
    background-image:
      radial-gradient(
        circle at var(--mx) var(--my),
        transparent calc(var(--size) * 1%),
        var(--bg-color) calc((var(--size) + var(--spread)) * 1%)),
      radial-gradient(
        var(--light-color) calc(var(--diameter) * 0.1px) calc((var(--diameter) * 0.1px) + 0.1px),
        transparent calc((var(--diameter) * 0.1px) + 0.2px));
    background-size: 100% 100%, calc(var(--spacing) * 1px) calc(var(--spacing) * 1px);
    transition: all 0.1s;
  }

  .radial-grid::before {
    --brightness: brightness(calc(max(var(--halftone) * 0.8, 1)));
    --blur: blur(calc(var(--halftone) * 3px));
    --contrast: contrast(calc(max(var(--halftone) * 999, 1)));
    content: "";
    position: absolute;
    inset: 0;
    backdrop-filter: var(--brightness) var(--blur) var(--contrast);
  }
`;

// Inject styles into document head
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = radialGridStyles;
  document.head.appendChild(styleElement);
}

const Niches = () => {
  const sectionRef = useRef(null);
  const gridRef = useRef(null);

  // Niche items with their corresponding SVG icons in German
  const nicheItems = [
    { text: 'Landwirtschaft', icon: 'tractor.svg' },
    { text: 'Human Resources', icon: 'users.svg' },
    { text: 'Bildung', icon: 'graduation-cap.svg' },
    { text: 'Versicherung', icon: 'house.svg' },
    { text: 'Immobilien', icon: 'building-2.svg' },
    { text: 'Hotellerie', icon: 'hotel.svg' },
    { text: 'Recht', icon: 'gavel.svg' },
    { text: 'Industrie', icon: 'working-factory-svgrepo-com 1.svg' },
    { text: 'Bauwesen', icon: 'pickaxe.svg' },
    { text: 'Gastronomie', icon: 'hamburger.svg' },
    { text: 'Gemeinden', icon: 'landmark.svg' },
    { text: 'Telekommunikation', icon: 'phone-call.svg' },
    { text: 'Einzelhandel', icon: 'store.svg' },
    { text: 'Gesundheitswesen', icon: 'hospital.svg' },
    { text: 'E-Commerce', icon: 'shopping-cart.svg' },
    { text: 'Finanzen', icon: 'piggy-bank.svg' },
    { text: 'Marketing', icon: 'monitor-play.svg' },
    { text: 'Energie', icon: 'house-plug.svg' },




  ].map(item => ({
    ...item,
    icon: (
      <img
        src={`/niches/${item.icon}`}
        alt={item.text}
        width="32"
        height="32"
        className="text-white"
      />
    )
  }));

  // Animation setup
  useEffect(() => {
    const cards = gridRef.current?.children;
    if (!cards) return;

    // Set initial positions - alternating columns pattern for both rows
    gsap.set(cards, {
      y: (index) => {
        const columnIndex = index % 9; // Which column (0,1,2,3,4,5,6,7,8)
        return columnIndex % 2 === 0 ? 60 : 0; // Columns 0,2,4,6,8 have 60px offset, columns 1,3,5,7 stay at 0
      },
      opacity: 0.7
    });

    // Create timeline for smooth animation
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: sectionRef.current,
        start: "top 70%",
        end: "center 30%",
        scrub: 1.5,
        markers: false
      }
    });

    // Animate cards to final positions - NO STAGGER, all move together
    tl.to(cards, {
      y: 0,
      opacity: 1,
      duration: 1,
      ease: "power2.out"
      // No stagger - all cards move in sync
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section ref={sectionRef} className="bg-[#000000] text-white px-4 py-20 mt-[-600px] relative z-30">
      <div className="max-w-7xl mx-auto text-center relative z-10">
        <div className="flex flex-col items-center justify-center">
          <div ref={gridRef} className="grid grid-cols-9 gap-[40px] justify-center">
          {nicheItems.map((item, index) => (
            <div
              key={index}
              className="bg-[#0D0E12] rounded-lg w-[160px] h-[160px] flex flex-col items-center justify-center gap-2"
            >
              <div className="w-8 h-8">
                {item.icon}
              </div>
              <span className="font-['Geist'] text-[12px] font-light text-[#787c87]">
                {item.text}
              </span>
            </div>
          ))}
          </div>
        </div>
      </div>

      {/* Spotlight Effect - Radial Grid Pattern */}
      <div className="radial-grid absolute inset-0 z-20 pointer-events-none" aria-hidden="true"></div>
    </section>
  );
};

export default Niches;
