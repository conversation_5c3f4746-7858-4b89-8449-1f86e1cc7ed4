const Niches = () => {
  // Niche items with their corresponding SVG icons in German
  const nicheItems = [
    { text: 'Landwirtschaft', icon: 'tractor.svg' },
    { text: 'Human Resources', icon: 'users.svg' },
    { text: 'Bildung', icon: 'graduation-cap.svg' },
    { text: 'Versicherung', icon: 'house.svg' },
    { text: 'Immobilien', icon: 'building-2.svg' },
    { text: 'Hotellerie', icon: 'hotel.svg' },
    { text: 'Recht', icon: 'gavel.svg' },
    { text: 'Industrie', icon: 'working-factory-svgrepo-com 1.svg' },
    { text: 'Bauwesen', icon: 'pickaxe.svg' },
    { text: 'Gastronomie', icon: 'hamburger.svg' },
    { text: 'Gemeinden', icon: 'landmark.svg' },
    { text: 'Telekommunikation', icon: 'phone-call.svg' },
    { text: 'Einzelhandel', icon: 'store.svg' },
    { text: 'Gesundheitswesen', icon: 'hospital.svg' },
    { text: 'E-Commerce', icon: 'shopping-cart.svg' },
    { text: 'Finanzen', icon: 'piggy-bank.svg' },
    { text: 'Marketing', icon: 'monitor-play.svg' },
    { text: 'Energie', icon: 'house-plug.svg' },




  ].map(item => ({
    ...item,
    icon: (
      <img 
        src={`/niches/${item.icon}`} 
        alt={item.text} 
        width="32" 
        height="32"
        className="text-white"
      />
    )
  }));

  return (
    <section className="min-h-screen bg-[#070709] text-white px-4 flex items-center justify-center">
      <div className="max-w-7xl mx-auto text-center">
        <div className="flex flex-col items-center justify-center">
          <h2 className="font-['CalSans'] text-[135px] leading-[110%] tracking-[6px] text-white mb-20">
            Anywhere.<br />Any Industry.
          </h2>
        
        <div className="grid grid-cols-9 gap-[7.5px] max-w-[1140px] mx-auto">
          {nicheItems.map((item, index) => (
            <div 
              key={index}
              className="bg-[#0D0E12] rounded-lg w-[120px] h-[120px] flex flex-col items-center justify-center gap-2"
            >
              <div className="w-8 h-8">
                {item.icon}
              </div>
              <span className="font-['Geist'] text-[12px] font-light text-[#787c87]">
                {item.text}
              </span>
            </div>
          ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Niches;
