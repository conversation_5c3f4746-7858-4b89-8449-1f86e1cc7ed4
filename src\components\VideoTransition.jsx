import { useEffect, useRef, useState } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Hls from 'hls.js';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

const VideoTransition = ({ className }) => {
  const sectionRef = useRef(null);
  const videoContainerRef = useRef(null);
  const videoRef = useRef(null);
  const scrollDirectionRef = useRef(0);
  const isScrollingDownRef = useRef(true);
  const [inView, setInView] = useState(false);

  // Track scroll direction
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.pageYOffset || document.documentElement.scrollTop;
      isScrollingDownRef.current = currentScrollY > scrollDirectionRef.current;
      scrollDirectionRef.current = currentScrollY;
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Initialize video with HLS
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !inView) return;

    const videoSrc = "/videotransition/video-2160p.m3u8";

    if (Hls.isSupported()) {
      const hls = new Hls({
        debug: false,
        enableWorker: true,
        lowLatencyMode: true,
      });

      hls.loadSource(videoSrc);
      hls.attachMedia(video);

      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      hls.on(Hls.Events.MANIFEST_PARSED, function() {
        if (!isMobile) {
          const highestLevel = hls.levels.length - 1;
          hls.currentLevel = highestLevel;
        }
      });

      hls.on(Hls.Events.ERROR, function(_, data) {
        console.warn('HLS error:', data);
        if (data.fatal) {
          switch(data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('Network error, trying to recover...');
              hls.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('Media error, trying to recover...');
              hls.recoverMediaError();
              break;
            default:
              console.log('Fatal error, destroying HLS instance');
              hls.destroy();
              break;
          }
        }
      });

      return () => {
        hls.destroy();
      };
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // Native HLS support (Safari)
      video.src = videoSrc;
    } else {
      console.warn('HLS not supported in this browser');
    }
  }, [inView]);

  // Setup ScrollTrigger for video control
  useEffect(() => {
    const video = videoRef.current;
    const section = sectionRef.current;

    if (!video || !section) return;

    const setupScrollTrigger = () => {
      // Responsive scroll distances based on screen size
      const scrollDistances = {
        "1439px": "+=1500",
        "767px": "+=3500",
        "479px": "+=2000"
      };

      let startDistance = "+=2200"; // default - start much earlier to catch beginning of video

      // Find matching breakpoint
      const matchingBreakpoint = Object.keys(scrollDistances)
        .sort((a, b) => parseInt(a) - parseInt(b))
        .find(breakpoint => window.matchMedia(`(max-width: ${breakpoint})`).matches);

      if (matchingBreakpoint) {
        startDistance = scrollDistances[matchingBreakpoint];
      }


      ScrollTrigger.create({
        start: startDistance,
        end: "bottom 100%-=100px",
        endTrigger: section,
        scrub: true,
        markers: true, // Add markers for debugging
        id: "video-scroll", // Add ID for easier identification
        onUpdate: (self) => {
          const progress = self.progress;
          const duration = video.duration;

          // Debug logging
          console.log(`Video progress: ${(progress * 100).toFixed(1)}%, currentTime: ${(duration * progress).toFixed(2)}s`);

          // Dispatch custom event for video progress
          const progressEvent = new CustomEvent('appsVideoProgress', {
            detail: { progress }
          });
          window.dispatchEvent(progressEvent);

          // Update video currentTime based on scroll progress
          if (Number.isFinite(duration * progress)) {
            video.currentTime = duration * progress;
          } else {
            video.currentTime = 0;
          }
        }
      });


    };

    const handleLoadedMetadata = () => setupScrollTrigger();
    video.addEventListener('loadedmetadata', handleLoadedMetadata);

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  // Intersection Observer for performance
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setInView(entry.isIntersecting);
      },
      {
        rootMargin: '0px 0px 900px 0px',
        threshold: 0
      }
    );

    if (videoContainerRef.current) {
      observer.observe(videoContainerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section
      className={`apps relative flex h-[7500px] flex-col justify-between lg:pb-0 mt-[-1500px] ${className || ''}`}
      ref={sectionRef}
    >
      <div
        className="sticky top-[calc(50%-800px)] w-full overflow-hidden bg-black md:top-[calc(50%-700px)] sm:top-[calc(50%-500px)] 2xs:top-[calc(50%-450px)]"
        ref={videoContainerRef}
      >
        <div>
          <video
            className="pointer-events-none relative left-1/2 z-20 w-full min-w-[1440px] max-w-[1440px] -translate-x-1/2 mix-blend-lighten [filter:brightness(0.95)_contrast(105%)_saturate(110%)] md:min-w-[1240px] sm:min-w-[740px] 2xs:min-w-[660px]"
            height={1440}
            width={1440}
            ref={videoRef}
            preload="metadata"
            playsInline
            muted
          />
        </div>
        <h2 className="absolute left-1/2 top-1/2 z-10 -mt-6 ml-1.5 -translate-x-1/2 -translate-y-1/2 text-center font-title text-[135px] leading-[90%] tracking-[6px] text-black mix-blend-lighten">
          Any app. Anywhere.
        </h2>
      </div>
    </section>
  );
};

export default VideoTransition;