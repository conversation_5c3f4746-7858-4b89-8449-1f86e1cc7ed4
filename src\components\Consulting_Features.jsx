const Consulting_Features = () => {
  const features = [
    {
      title: "Prozess & Workflow Analysis",
      description: "Wir schauen uns deine Abläufe an, decken unnötige Routine- aufgaben auf und finden die Stellen, an denen KI wirklich Wirkung entfaltet.",
      image: "/consulting_features/ProcessCard.png"
    },
    {
      title: "KI Audit & Report",
      description: "Du erhältst einen verständlichen, datenbasierten Report, der dir genau zeigt, wo du stehst und wo KI das größte Potential hat.",
      image: "/consulting_features/AIAuditCard.png"
    },
    {
      title: "KI AUDIT",
      image: "/consulting_features/AIAuditCard.png"
    },
    {
      title: "BLCKS KI Strategie",
      description: "Mit klarer KI-Strategie erreichst du Ergebnisse bis zu 3x schneller. Ohne endlose Experimente oder Fehlinvestitionen.",
      image: "/consulting_features/3xCard.png"
    },
    {
      title: "KI Roadmap Development",
      description: "Gemeinsam entwickeln wir eine klare Strategie, die zeigt, wie KI dir heute Zeit spart und morgen Wachstum bringt.",
      image: "/consulting_features/RoadmapCard.png"
    },
    {
      title: "Wachstums & Umsatzpotenziale",
      description: "Wir denken über Kostensenkung hinaus und zeigen dir, wie du mit KI mehr Kunden gewinnst, Umsätze steigerst und nachhaltig wachsen kannst.",
      image: "/consulting_features/GrowthCard.png"
    }
  ];

  return (
    <section className="min-h-[200vh] bg-[#070709] text-white px-4 relative">
      <div className="max-w-[960px] mx-auto py-20 relative z-10">
        <div className="relative w-full">
          {/* Left Column */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-8 pt-[256px]">
              {features.slice(0, 3).map((feature, index) => (
                <div 
                  key={`left-${index}`}
                  className="rounded-2xl w-[464px] h-[512px] p-6 flex flex-col justify-end"
                  style={{
                    backgroundImage: `url(${feature.image})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center'
                  }}
                >
                  <p className="font-['Geist'] text-base tracking-[0.00em]">
                    <span className="font-medium text-white">{feature.title}.</span> {feature.description && (
                      <span className="font-light text-[#ADB0B9]"> {feature.description}</span>
                    )}
                  </p>
                </div>
              ))}
            </div>
            
            {/* Right Column */}
            <div className="space-y-8">
              {features.slice(3, 6).map((feature, index) => (
                <div 
                  key={`right-${index}`}
                  className="rounded-2xl w-[464px] h-[512px] p-6 flex flex-col justify-end"
                  style={{
                    backgroundImage: `url(${feature.image})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center'
                  }}
                >
                  <p className="font-['Geist'] text-base tracking-[0.00em]">
                    <span className="font-medium text-white">{feature.title}.</span> {feature.description && (
                      <span className="font-light text-[#ADB0B9]"> {feature.description}</span>
                    )}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Consulting_Features;
