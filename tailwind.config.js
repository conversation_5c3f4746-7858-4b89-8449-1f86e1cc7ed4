/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      screens: {
        '2xs': '480px',
      },
      fontFamily: {
        'sans': ['Geist', 'sans-serif'],
        'geist': ['Geist', 'sans-serif'],
        'calsans': ['CalSans', 'sans-serif'],
        'title': ['CalSans', 'sans-serif'],
      },
      fontSize: {
        '84': '5.25rem',
        '72': '4.5rem',
        '56': '3.5rem',
        '32': '2rem',
        '18': '1.125rem',
        '16': '1rem',
        '15': '0.9375rem',
      },
      colors: {
        'grey-1': '#070709',
        'grey-90': 'rgba(255, 255, 255, 0.9)',
      },
      padding: {
        'safe': 'env(safe-area-inset)'
      },
      margin: {
        'safe': 'env(safe-area-inset)'
      },
      lineHeight: {
        '0.9': '0.9',
      },
      letterSpacing: {
        'tighter': '-0.02em',
      }
    },
  },
  plugins: [],
}
