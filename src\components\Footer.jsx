import React from "react";

const Footer = () => {
  const productLinks = [
    "Continuous Delivery & GitOps",
    "Continuous Integration",
    "Cloud Cost Management",
    "Feature Flags",
    "Chaos Engineering",
    "Security Testing Orchestration",
    "Software Engineering Insights",
    "Platform"
  ];

  const companyLinks = [
    "About Us",
    "Press & News",
    "Partners",
    "Careers",
    "Contact Us",
    "Customers",
    "Trademark Policy",
    "Security Legal"
  ];

  const resourceLinks = [
    "Documentation",
    "API Reference Docs",
    "Comparison Guide",
    "Blog",
    "Developer Hub",
    "Community",
    "On-demand Videos",
    "Collateral",
    "Harness Certifications",
    "Harness Support"
  ];

  const bottomLinks = [
    "Subscription Terms",
    "Website Terms of Use",
    "Privacy Policy",
    "Do not sell or share my personal information",
    "Cookie Settings"
  ];

  return (
    <footer className="relative overflow-hidden pt-14 px-4 pb-12 sm:pt-12 bg-cover bg-center" style={{ backgroundImage: 'url(/bg-footer-1.png)' }}>
      <div className="absolute inset-0 bg-[#070709] opacity-75" />
      <span className="absolute left-0 top-0 z-10 h-px w-full bg-white mix-blend-overlay" aria-hidden="true" />
      <span className="absolute inset-0 -z-10 h-full w-full backdrop-blur-[35px] after:absolute after:inset-0 after:bg-black/70" aria-hidden="true" />

      <div className="max-w-[1408px] mx-auto px-8 relative z-10">
        <div className="flex justify-between w-full">
          {/* Left Column - Logo and social */}
          <div className="w-[252px] flex flex-col justify-between" style={{ minHeight: 'calc(291px + 24px + 24px)' }}>
            <a href="/" className="block">
              <img 
                src="/icons/BLCKS_Logo.svg" 
                alt="BLCKS Logo"
                className="h-6 w-auto"
              />
            </a>
            
            <div className="flex gap-x-7">
              <a href="#" aria-label="GitHub" rel="noopener noreferrer">
                <img src="/icons/github.svg" alt="GitHub" className="w-6 h-6" />
              </a>
              <a href="#" aria-label="LinkedIn" rel="noopener noreferrer">
                <img src="/icons/linkedin.svg" alt="LinkedIn" className="w-6 h-6" />
              </a>
              <a href="#" aria-label="Twitter" rel="noopener noreferrer">
                <img src="/icons/x.svg" alt="X (Twitter)" className="w-6 h-6" />
              </a>
            </div>
          </div>
          
          {/* Right Column - Navigation Links */}
          <div className="flex-1">
            <div className="flex justify-end" style={{ gap: '136px' }}>
            {/* Product Links */}
            <div className="w-auto">
              <h3 className="font-['Geist',Helvetica] font-medium text-white text-[15px] tracking-[-0.32px] leading-[19.5px] mb-5">
                Produkt
              </h3>
              <div className="flex flex-col" style={{ gap: '1rem' }}>
                {productLinks.map((link, index) => (
                  <a
                    key={`product-${index}`}
                    href="#"
                    className="font-['Geist',Helvetica] font-light text-[#adb0b9] text-[15px] tracking-[-0.32px] leading-[15px] hover:text-white transition-colors"
                  >
                    {link}
                  </a>
                ))}
              </div>
            </div>

            {/* Company Links */}
            <div className="w-auto">
              <h3 className="font-['Geist',Helvetica] font-medium text-white text-[15px] tracking-[-0.32px] leading-[19.5px] mb-5">
                Company
              </h3>
              <div className="flex flex-col" style={{ gap: '1rem' }}>
                {companyLinks.map((link, index) => (
                  <a
                    key={`company-${index}`}
                    href="#"
                    className="font-['Geist',Helvetica] font-light text-[#adb0b9] text-[15px] tracking-[-0.32px] leading-[15px] hover:text-white transition-colors"
                  >
                    {link}
                  </a>
                ))}
              </div>
            </div>

            {/* Resources Links */}
            <div className="w-auto">
              <h3 className="font-['Geist',Helvetica] font-medium text-white text-[15px] tracking-[-0.32px] leading-[19.5px] mb-5">
                Resources
              </h3>
              <div className="flex flex-col" style={{ gap: '1rem' }}>
                {resourceLinks.map((link, index) => (
                  <a
                    key={`resource-${index}`}
                    href="#"
                    className="font-['Geist',Helvetica] font-light text-[#adb0b9] text-[15px] tracking-[-0.32px] leading-[15px] hover:text-white transition-colors"
                  >
                    {link}
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom section */}
        <div className="w-full max-w-[1344px] mx-auto mt-10 pt-10 border-t border-[#16181D]">
          <div className="flex items-center justify-between">
            <div className="font-['Geist'] font-light text-[#787c87] text-[14px] tracking-[-0.02em] leading-[18.2px]">
              © 2024 BLCKS, Designed by{' '}
              <a 
                href="https://pixelpoint.io/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-[#787c87] hover:text-white transition-colors"
              >
                Pixel Point
              </a>
            </div>
            
            <div className="flex items-center gap-[30px]">
              {bottomLinks.map((link, index) => (
                <a 
                  key={`bottom-${index}`}
                  className="font-['Geist'] font-light text-[#787c87] text-[14px] tracking-[-0.02em] leading-[18.2px] hover:text-white transition-colors" 
                  href="#"
                >
                  {link}
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
