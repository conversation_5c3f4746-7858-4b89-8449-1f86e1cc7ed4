<svg width="245" height="5" viewBox="0 0 245 5" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect y="3" width="1" height="241" rx="0.5" transform="rotate(-90 0 3)" fill="#787C87" fill-opacity="0.2"/>
<rect y="3" width="1" height="241" rx="0.5" transform="rotate(-90 0 3)" fill="url(#paint0_linear_324_1819)"/>
<g filter="url(#filter0_f_324_1819)">
<ellipse cx="228" cy="2.25" rx="15" ry="0.25" fill="white"/>
</g>
<defs>
<filter id="filter0_f_324_1819" x="211" y="0" width="34" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_324_1819"/>
</filter>
<linearGradient id="paint0_linear_324_1819" x1="0.5" y1="3" x2="0.5" y2="244" gradientUnits="userSpaceOnUse">
<stop offset="0.235577" stop-color="#787C87"/>
<stop offset="0.716346" stop-color="#BCBEC3"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
