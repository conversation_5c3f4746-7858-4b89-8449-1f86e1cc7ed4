import { useEffect, useRef } from 'react';
import gsap from 'gsap';
import { Draggable } from 'gsap/Draggable';
import { InertiaPlugin } from 'gsap/InertiaPlugin';
import './HowItWorks.css';

// Register GSAP plugins
gsap.registerPlugin(Draggable, InertiaPlugin);

const HowItWorks = () => {
  const steps = [
    {
      number: "Analyse",
      title: "Dein Business verstehen",
      description: "Wir starten mit einem strukturierten Deep Dive in deine Abläufe. Von Vertrieb über Backoffice bis zum Kundenservice: Wir identifizieren Bottlenecks, repetitive Aufgaben und ungenutzte Potenziale. Das Ergebnis ist ein klarer Überblick darüber, wo KI wirklich Wirkung entfalten kann. Faktenbasiert, statt nur auf Hypothesen.",
      icon: "/icons/Step1_Icon.svg"
    },
    {
      number: "Roadmap",
      title: "Eine klare AI-Strategie entwickeln",
      description: "Auf Basis der Analyse entwerfen wir eine Roadmap, die zu deinem Unternehmen passt. Keine überladenen Strategiepapiere, sondern ein klarer, priorisierter Plan: Welche Prozesse automatisiert werden, wo Umsatzpotenziale liegen und wie dein Team sofort entlastet wird. Verständlich, praxisnah und mit Fokus auf messbare Ergebnisse.",
      icon: "/icons/Step2_Icon.svg"
    },
    {
      number: "Integration",
      title: "KI-Blocks nahtlos implementieren",
      description: "Gemeinsam mit deinem Team setzen wir die passenden KI-Blocks direkt in deine Systeme ein. Schnell, reibungslos und ohne komplizierte Umstellungen. Alles läuft reibungslos im Hintergrund, ohne dass du bestehende Strukturen auf den Kopf stellen musst. So entsteht Schritt für Schritt dein KI-Stack, der sofort Mehrwert bringt.",
      icon: "/icons/Step3_Icon.svg"
    },
    {
      number: "Skalierung",
      title: "Ergebnisse messen und skalieren",
      description: "Nach der Implementierung geht es um Wachstum. Wir messen die erzielten Effekte, optimieren kontinuierlich und erweitern dein Setup um neue KI-Blocks. So stellst du sicher, dass dein Unternehmen nicht nur Kosten spart, sondern auch mehr Kunden gewinnt und nachhaltig skalieren kann.",
      icon: "/icons/Step4_Icon.svg"
    }
  ];

  const sliderRef = useRef(null);
  const containerRef = useRef(null);
  const nextButtonRef = useRef(null);
  const prevButtonRef = useRef(null);
  const draggableRef = useRef(null);

  useEffect(() => {
    // Initialize slider after component mounts
    const initSlider = () => {
      const container = containerRef.current;
      const slider = sliderRef.current;
      const nextButton = nextButtonRef.current;
      const prevButton = prevButtonRef.current;
      
      if (!container || !slider || !nextButton || !prevButton) {
        return;
      }

      // Set initial state
      let currentIndex = 0;
      const slideWidth = 620; // 600px width + 20px margin
      const totalSlides = steps.length;
      
      // Position slides horizontally
      const slides = slider.children;
      for (let i = 0; i < slides.length; i++) {
        slides[i].style.position = 'absolute';
        slides[i].style.left = `${i * slideWidth}px`;
      }
      
      // Set container width
      slider.style.width = `${totalSlides * slideWidth}px`;
      
      // Create draggable
      const draggable = Draggable.create(slider, {
        type: "x",
        bounds: {
          minX: -(totalSlides - 1) * slideWidth,
          maxX: 0
        },
        inertia: true,
        snap: {
          x: (value) => Math.round(value / slideWidth) * slideWidth
        },
        onDragEnd: function() {
          // Calculate current index based on position
          const endPosition = this.x;
          currentIndex = Math.round(Math.abs(endPosition) / slideWidth);
          currentIndex = Math.max(0, Math.min(currentIndex, totalSlides - 1));
        }
      })[0];
      
      draggableRef.current = draggable;
      
      // Button handlers
      const goToNext = () => {
        if (currentIndex < totalSlides - 1) {
          currentIndex++;
          gsap.to(slider, {
            x: -currentIndex * slideWidth,
            duration: 0.7,
            ease: "power2.out"
          });
        }
      };
      
      const goToPrev = () => {
        if (currentIndex > 0) {
          currentIndex--;
          gsap.to(slider, {
            x: -currentIndex * slideWidth,
            duration: 0.7,
            ease: "power2.out"
          });
        }
      };
      
      // Add event listeners
      nextButton.addEventListener('click', goToNext);
      prevButton.addEventListener('click', goToPrev);
    };

    // Initialize after a short delay to ensure DOM is ready
    const timer = setTimeout(initSlider, 100);
    
    // Cleanup function
    return () => {
      clearTimeout(timer);
      if (draggableRef.current) {
        draggableRef.current.kill();
      }
    };
  }, [steps]);

  return (
    <section className="min-h-[75vh] bg-[#070709] text-white py-20 px-4">
      <div className="max-w-[1280px] mx-auto">
        {/* Header Section */}
        <div className="mb-[100px]">
          <span className="font-['Geist'] font-light text-sm text-white tracking-[0.02em] pb-3 block">
            WIE BLCKS ARBEITET
          </span>
          <h2 className="font-['Geist'] font-medium text-[48px] text-white tracking-[-0.02em] text-left mb-6">
          Consulting that turns into action.
          </h2>
          <p className="font-['Geist'] font-normal text-base text-[#ADB0B9] max-w-[700px] pb-[0px]">
            Keine endlosen Reports, sondern direkte Umsetzung. Wir verbinden strategische Beratung mit technischer Umsetzung. So weißt du nicht nur, was möglich ist, sondern siehst schnell echte Resultate in deinem Tagesgeschäft.
          </p>
        </div>

        {/* Slider Controls */}
        <div className="flex justify-end items-center mb-6">
          <div className="flex gap-4">
            <button 
              ref={prevButtonRef}
              aria-label="previous slide" 
              className="w-10 h-10 rounded-full bg-white flex items-center justify-center shadow-lg hover:opacity-90 transition-opacity"
            >
              <img src="/icons/button-arrow-right.svg" alt="Previous" className="w-4 h-4 rotate-180" />
            </button>
            
            <button 
              ref={nextButtonRef}
              aria-label="next slide" 
              className="w-10 h-10 rounded-full bg-white flex items-center justify-center shadow-lg hover:opacity-90 transition-opacity"
            >
              <img src="/icons/button-arrow-right.svg" alt="Next" className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Slider */}
        <div className="main w-full overflow-hidden relative h-[450px]">
          <div className="slider-wrap w-full h-full overflow-hidden">
            <div 
              ref={containerRef}
              className="slider-list w-full h-full relative"
            >
              <div 
                ref={sliderRef}
                className="absolute top-0 left-0 h-full"
              >
                {steps.map((step, index) => (
                  <div 
                    key={index}
                    className="absolute top-0 h-full"
                    style={{ width: '600px', marginRight: '20px' }}
                  >
                    <div 
                      className="rounded-2xl w-full h-[400px] relative overflow-hidden"
                      style={{
                        backgroundImage: `url(/howitworks/HOWITWORKS_CARD_STEP${index + 1}.png)`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        backgroundRepeat: 'no-repeat'
                      }}
                    >
                      {/* Semi-transparent overlay to ensure text remains readable */}
                      <div className="absolute inset-0 bg-black/20"></div>
                      {/* Top Left Icon */}
                      <div className="absolute top-5 left-5 z-10">
                        <img 
                          src={step.icon} 
                          alt={`Step ${index + 1}`} 
                          className="w-10 h-10"
                        />
                      </div>

                      {/* Vertical Line */}
                      <div className="absolute top-0 bottom-0 left-20 w-px" style={{ backgroundColor: 'rgba(120, 124, 135, 0.25)' }}></div>

                      {/* Vertical Text */}
                      <div className="absolute bottom-5 left-5 w-6 h-32 overflow-visible z-10">
                        <span className="absolute bottom-0 left-0 font-['CalSans'] text-[26px] font-normal tracking-[0.02em] text-white transform -rotate-90 origin-bottom-left whitespace-nowrap translate-x-10 translate-y-0">
                          {step.number}
                        </span>
                      </div>

                      {/* Content */}
                      <div className="absolute top-8 left-[100px] right-5 z-10">
                        <h3 className="font-['Geist'] font-normal text-base text-white tracking-[-0.02em] mb-3.5">
                          {step.title}
                        </h3>
                        <p className="font-['Geist'] font-normal text-[13px] text-[#787c87] tracking-[-0.02em] max-w-[475px]">
                          {step.description}
                        </p>
                      </div>

                      {/* Bottom Right Text */}
                      <div className="absolute bottom-[30px] right-5 w-[calc(100%-100px)] z-10">
                        <p className="font-['Geist'] font-normal text-[10px] text-[#787c87] text-right tracking-[0.01em] opacity-25">
                          {index === 0 ? '25%' : index === 1 ? '50%' : index === 2 ? '75%' : '100%'}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;